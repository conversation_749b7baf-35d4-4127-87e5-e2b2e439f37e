"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";

export default function SeedDataPage() {
  const [isSeeding, setIsSeeding] = useState(false);
  const [message, setMessage] = useState({ type: "", text: "" });

  const seedExaminations = useMutation(api.seedData.seedExaminations);

  const handleSeedData = async () => {
    setIsSeeding(true);
    setMessage({ type: "", text: "" });

    try {
      const result = await seedExaminations();
      setMessage({
        type: "success",
        text: `Successfully seeded ${result.insertedCount} examinations!`
      });
    } catch (error) {
      setMessage({
        type: "error",
        text: error.message || "Failed to seed data"
      });
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-8">
        <h1 className="text-2xl font-bold text-gray-800 mb-6 text-center">
          Seed Sample Data
        </h1>
        
        {message.text && (
          <div className={`mb-6 p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-100 text-green-700 border border-green-300' 
              : 'bg-red-100 text-red-700 border border-red-300'
          }`}>
            {message.text}
          </div>
        )}

        <div className="text-center">
          <p className="text-gray-600 mb-6">
            Click the button below to seed the database with sample JKSSB and JKPSC examinations.
          </p>
          
          <button
            onClick={handleSeedData}
            disabled={isSeeding}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
          >
            {isSeeding ? "Seeding Data..." : "Seed Sample Data"}
          </button>

          <div className="mt-6">
            <a
              href="/"
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              ← Back to Homepage
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
