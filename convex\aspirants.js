import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Register a new aspirant
export const registerAspirant = mutation({
  args: {
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    phone: v.string(),
    dateOfBirth: v.string(),
    gender: v.union(v.literal("male"), v.literal("female"), v.literal("other")),
    category: v.union(
      v.literal("General"),
      v.literal("OBC"),
      v.literal("SC"),
      v.literal("ST"),
      v.literal("EWS")
    ),
    address: v.object({
      street: v.string(),
      city: v.string(),
      district: v.string(),
      state: v.string(),
      pincode: v.string(),
    }),
    qualification: v.object({
      degree: v.string(),
      university: v.string(),
      percentage: v.number(),
      yearOfPassing: v.number(),
    }),
    profilePhoto: v.optional(v.string()),
    signature: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if email already exists
    const existingAspirant = await ctx.db
      .query("aspirants")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
    
    if (existingAspirant) {
      throw new Error("An account with this email already exists");
    }
    
    // Check if phone already exists
    const existingPhone = await ctx.db
      .query("aspirants")
      .withIndex("by_phone", (q) => q.eq("phone", args.phone))
      .first();
    
    if (existingPhone) {
      throw new Error("An account with this phone number already exists");
    }
    
    const now = new Date().toISOString();
    
    return await ctx.db.insert("aspirants", {
      ...args,
      isVerified: false,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Get aspirant by email (for login)
export const getAspirantByEmail = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("aspirants")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
  },
});

// Get aspirant by ID
export const getAspirantById = query({
  args: { id: v.id("aspirants") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Update aspirant profile
export const updateAspirantProfile = mutation({
  args: {
    id: v.id("aspirants"),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    phone: v.optional(v.string()),
    address: v.optional(v.object({
      street: v.string(),
      city: v.string(),
      district: v.string(),
      state: v.string(),
      pincode: v.string(),
    })),
    qualification: v.optional(v.object({
      degree: v.string(),
      university: v.string(),
      percentage: v.number(),
      yearOfPassing: v.number(),
    })),
    profilePhoto: v.optional(v.string()),
    signature: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    const now = new Date().toISOString();
    
    // Remove undefined values
    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );
    
    return await ctx.db.patch(id, {
      ...cleanUpdates,
      updatedAt: now,
    });
  },
});

// Verify aspirant account
export const verifyAspirant = mutation({
  args: { id: v.id("aspirants") },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    return await ctx.db.patch(args.id, {
      isVerified: true,
      updatedAt: now,
    });
  },
});

// Get all aspirants (admin function)
export const getAllAspirants = query({
  args: {
    limit: v.optional(v.number()),
    isVerified: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("aspirants");
    
    if (args.isVerified !== undefined) {
      query = query.filter((q) => q.eq(q.field("isVerified"), args.isVerified));
    }
    
    if (args.limit) {
      return await query.order("desc").take(args.limit);
    }
    
    return await query.order("desc").collect();
  },
});

// Search aspirants by name or email
export const searchAspirants = query({
  args: { searchTerm: v.string() },
  handler: async (ctx, args) => {
    const aspirants = await ctx.db.query("aspirants").collect();
    
    return aspirants.filter(aspirant => 
      aspirant.firstName.toLowerCase().includes(args.searchTerm.toLowerCase()) ||
      aspirant.lastName.toLowerCase().includes(args.searchTerm.toLowerCase()) ||
      aspirant.email.toLowerCase().includes(args.searchTerm.toLowerCase())
    );
  },
});

// Get aspirant statistics
export const getAspirantStats = query({
  args: {},
  handler: async (ctx) => {
    const allAspirants = await ctx.db.query("aspirants").collect();
    
    const totalAspirants = allAspirants.length;
    const verifiedAspirants = allAspirants.filter(a => a.isVerified).length;
    const pendingVerification = totalAspirants - verifiedAspirants;
    
    // Category-wise breakdown
    const categoryStats = allAspirants.reduce((acc, aspirant) => {
      acc[aspirant.category] = (acc[aspirant.category] || 0) + 1;
      return acc;
    }, {});
    
    // Gender-wise breakdown
    const genderStats = allAspirants.reduce((acc, aspirant) => {
      acc[aspirant.gender] = (acc[aspirant.gender] || 0) + 1;
      return acc;
    }, {});
    
    return {
      totalAspirants,
      verifiedAspirants,
      pendingVerification,
      categoryStats,
      genderStats,
    };
  },
});
