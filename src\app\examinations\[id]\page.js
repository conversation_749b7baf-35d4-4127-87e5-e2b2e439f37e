"use client";

import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { useParams } from "next/navigation";
import Header from "../../components/Header";
import Footer from "../../components/Footer";
import LoadingSpinner from "../../components/LoadingSpinner";

export default function ExaminationDetailPage() {
  const params = useParams();
  const examId = params.id;

  const examination = useQuery(api.examinations.getExaminationById, 
    examId ? { id: examId } : "skip"
  );

  if (examination === undefined) {
    return <LoadingSpinner />;
  }

  if (examination === null) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              Examination Not Found
            </h1>
            <p className="text-gray-600 mb-8">
              The examination you're looking for doesn't exist or has been removed.
            </p>
            <a
              href="/examinations"
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Browse All Examinations
            </a>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'registration_open':
        return 'bg-green-100 text-green-800 border-green-300';
      case 'upcoming':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'registration_closed':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg p-8 mb-8">
            <div className="flex justify-between items-start mb-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(examination.status)}`}>
                {examination.status.replace('_', ' ').toUpperCase()}
              </span>
              <span className="bg-white bg-opacity-20 px-3 py-1 rounded text-sm font-medium">
                {examination.examType}
              </span>
            </div>
            <h1 className="text-3xl font-bold mb-2">{examination.title}</h1>
            <p className="text-xl opacity-90">{examination.description}</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Key Information */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-semibold text-gray-800 mb-6">Key Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Category</h3>
                    <p className="text-lg font-semibold text-gray-800">{examination.category}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Total Seats</h3>
                    <p className="text-lg font-semibold text-gray-800">{examination.totalSeats}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Available Seats</h3>
                    <p className="text-lg font-semibold text-green-600">{examination.availableSeats}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Application Fee</h3>
                    <p className="text-lg font-semibold text-gray-800">₹{examination.applicationFee}</p>
                  </div>
                </div>
              </div>

              {/* Exam Pattern */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-semibold text-gray-800 mb-6">Exam Pattern</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center bg-blue-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-blue-600">{examination.totalQuestions}</div>
                    <div className="text-sm text-gray-600">Total Questions</div>
                  </div>
                  <div className="text-center bg-green-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-green-600">{examination.duration}</div>
                    <div className="text-sm text-gray-600">Minutes</div>
                  </div>
                  <div className="text-center bg-purple-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-purple-600">{examination.marksPerQuestion}</div>
                    <div className="text-sm text-gray-600">Marks/Question</div>
                  </div>
                  <div className="text-center bg-red-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-red-600">
                      {examination.negativeMarking ? examination.negativeMarks : 'No'}
                    </div>
                    <div className="text-sm text-gray-600">Negative Marking</div>
                  </div>
                </div>
              </div>

              {/* Syllabus */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-semibold text-gray-800 mb-6">Syllabus</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {examination.syllabus.map((topic, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">{topic}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Eligibility Criteria */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-semibold text-gray-800 mb-6">Eligibility Criteria</h2>
                <div className="space-y-2">
                  {examination.eligibilityCriteria.map((criteria, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <svg className="w-4 h-4 text-blue-500 mt-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">{criteria}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Important Dates */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Important Dates</h2>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Registration Start</h3>
                    <p className="text-gray-800 font-medium">{formatDate(examination.registrationStartDate)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Registration End</h3>
                    <p className="text-gray-800 font-medium">{formatDate(examination.registrationEndDate)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Exam Date</h3>
                    <p className="text-gray-800 font-medium">{formatDate(examination.examDate)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Exam Time</h3>
                    <p className="text-gray-800 font-medium">{examination.examTime}</p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Actions</h2>
                <div className="space-y-3">
                  {examination.status === 'registration_open' && (
                    <a
                      href={`/examinations/${examination._id}/register`}
                      className="block w-full bg-green-600 text-white text-center py-3 rounded-lg hover:bg-green-700 transition-colors font-medium"
                    >
                      Register for Exam
                    </a>
                  )}
                  
                  <a
                    href="/examinations"
                    className="block w-full bg-gray-600 text-white text-center py-3 rounded-lg hover:bg-gray-700 transition-colors font-medium"
                  >
                    Back to All Exams
                  </a>
                  
                  <button
                    onClick={() => window.print()}
                    className="block w-full bg-blue-600 text-white text-center py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                  >
                    Print Details
                  </button>
                </div>
              </div>

              {/* Contact Information */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Need Help?</h2>
                <div className="space-y-3 text-sm">
                  <div>
                    <p className="text-gray-600">For technical support:</p>
                    <p className="font-medium text-blue-600"><EMAIL></p>
                  </div>
                  <div>
                    <p className="text-gray-600">For exam queries:</p>
                    <p className="font-medium text-blue-600"><EMAIL></p>
                  </div>
                  <div>
                    <p className="text-gray-600">Helpline:</p>
                    <p className="font-medium text-blue-600">1800-XXX-XXXX</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
