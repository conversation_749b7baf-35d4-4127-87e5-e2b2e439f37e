import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Register for an examination
export const registerForExam = mutation({
  args: {
    aspirantId: v.id("aspirants"),
    examinationId: v.id("examinations"),
  },
  handler: async (ctx, args) => {
    // Check if aspirant exists and is verified
    const aspirant = await ctx.db.get(args.aspirantId);
    if (!aspirant) {
      throw new Error("Aspirant not found");
    }
    if (!aspirant.isVerified) {
      throw new Error("Please verify your account before registering for exams");
    }
    
    // Check if examination exists and registration is open
    const examination = await ctx.db.get(args.examinationId);
    if (!examination) {
      throw new Error("Examination not found");
    }
    if (examination.status !== "registration_open") {
      throw new Error("Registration is not open for this examination");
    }
    if (examination.availableSeats <= 0) {
      throw new Error("No seats available for this examination");
    }
    
    // Check if already registered
    const existingRegistration = await ctx.db
      .query("examRegistrations")
      .withIndex("by_aspirant", (q) => q.eq("aspirantId", args.aspirantId))
      .filter((q) => q.eq(q.field("examinationId"), args.examinationId))
      .first();
    
    if (existingRegistration) {
      throw new Error("You have already registered for this examination");
    }
    
    // Generate registration number
    const registrationNumber = `${examination.examType}${Date.now()}${Math.floor(Math.random() * 1000)}`;
    
    const now = new Date().toISOString();
    
    // Create registration
    const registrationId = await ctx.db.insert("examRegistrations", {
      aspirantId: args.aspirantId,
      examinationId: args.examinationId,
      registrationNumber,
      applicationStatus: "submitted",
      paymentStatus: "pending",
      admitCardGenerated: false,
      registeredAt: now,
      updatedAt: now,
    });
    
    // Update available seats
    await ctx.db.patch(args.examinationId, {
      availableSeats: examination.availableSeats - 1,
      updatedAt: now,
    });
    
    return registrationId;
  },
});

// Get registrations for an aspirant
export const getAspirantRegistrations = query({
  args: { aspirantId: v.id("aspirants") },
  handler: async (ctx, args) => {
    const registrations = await ctx.db
      .query("examRegistrations")
      .withIndex("by_aspirant", (q) => q.eq("aspirantId", args.aspirantId))
      .collect();
    
    // Get examination details for each registration
    const registrationsWithExams = await Promise.all(
      registrations.map(async (registration) => {
        const examination = await ctx.db.get(registration.examinationId);
        return {
          ...registration,
          examination,
        };
      })
    );
    
    return registrationsWithExams;
  },
});

// Get registrations for an examination
export const getExaminationRegistrations = query({
  args: { examinationId: v.id("examinations") },
  handler: async (ctx, args) => {
    const registrations = await ctx.db
      .query("examRegistrations")
      .withIndex("by_examination", (q) => q.eq("examinationId", args.examinationId))
      .collect();
    
    // Get aspirant details for each registration
    const registrationsWithAspirants = await Promise.all(
      registrations.map(async (registration) => {
        const aspirant = await ctx.db.get(registration.aspirantId);
        return {
          ...registration,
          aspirant,
        };
      })
    );
    
    return registrationsWithAspirants;
  },
});

// Get registration by registration number
export const getRegistrationByNumber = query({
  args: { registrationNumber: v.string() },
  handler: async (ctx, args) => {
    const registration = await ctx.db
      .query("examRegistrations")
      .withIndex("by_registration_number", (q) => q.eq("registrationNumber", args.registrationNumber))
      .first();
    
    if (!registration) {
      return null;
    }
    
    const [aspirant, examination] = await Promise.all([
      ctx.db.get(registration.aspirantId),
      ctx.db.get(registration.examinationId),
    ]);
    
    return {
      ...registration,
      aspirant,
      examination,
    };
  },
});

// Update payment status
export const updatePaymentStatus = mutation({
  args: {
    registrationId: v.id("examRegistrations"),
    paymentStatus: v.union(
      v.literal("pending"),
      v.literal("completed"),
      v.literal("failed")
    ),
    paymentId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    const { registrationId, ...updates } = args;
    
    return await ctx.db.patch(registrationId, {
      ...updates,
      updatedAt: now,
    });
  },
});

// Update application status
export const updateApplicationStatus = mutation({
  args: {
    registrationId: v.id("examRegistrations"),
    applicationStatus: v.union(
      v.literal("submitted"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected")
    ),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    
    return await ctx.db.patch(args.registrationId, {
      applicationStatus: args.applicationStatus,
      updatedAt: now,
    });
  },
});

// Generate admit card
export const generateAdmitCard = mutation({
  args: {
    registrationId: v.id("examRegistrations"),
    examCenter: v.string(),
    seatNumber: v.string(),
  },
  handler: async (ctx, args) => {
    const registration = await ctx.db.get(args.registrationId);
    if (!registration) {
      throw new Error("Registration not found");
    }
    
    if (registration.applicationStatus !== "approved") {
      throw new Error("Application must be approved before generating admit card");
    }
    
    if (registration.paymentStatus !== "completed") {
      throw new Error("Payment must be completed before generating admit card");
    }
    
    const now = new Date().toISOString();
    
    return await ctx.db.patch(args.registrationId, {
      examCenter: args.examCenter,
      seatNumber: args.seatNumber,
      admitCardGenerated: true,
      updatedAt: now,
    });
  },
});

// Get registration statistics
export const getRegistrationStats = query({
  args: { examinationId: v.optional(v.id("examinations")) },
  handler: async (ctx, args) => {
    let query = ctx.db.query("examRegistrations");
    
    if (args.examinationId) {
      query = query.withIndex("by_examination", (q) => q.eq("examinationId", args.examinationId));
    }
    
    const registrations = await query.collect();
    
    const totalRegistrations = registrations.length;
    const approvedRegistrations = registrations.filter(r => r.applicationStatus === "approved").length;
    const pendingRegistrations = registrations.filter(r => r.applicationStatus === "submitted").length;
    const rejectedRegistrations = registrations.filter(r => r.applicationStatus === "rejected").length;
    const completedPayments = registrations.filter(r => r.paymentStatus === "completed").length;
    const admitCardsGenerated = registrations.filter(r => r.admitCardGenerated).length;
    
    return {
      totalRegistrations,
      approvedRegistrations,
      pendingRegistrations,
      rejectedRegistrations,
      completedPayments,
      admitCardsGenerated,
    };
  },
});
