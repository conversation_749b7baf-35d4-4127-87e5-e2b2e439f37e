import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Get questions for an examination
export const getExaminationQuestions = query({
  args: { examinationId: v.id("examinations") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("questions")
      .withIndex("by_examination", (q) => q.eq("examinationId", args.examinationId))
      .collect();
  },
});

// Create a new question
export const createQuestion = mutation({
  args: {
    examinationId: v.id("examinations"),
    questionText: v.string(),
    options: v.array(v.object({
      text: v.string(),
      isCorrect: v.boolean(),
    })),
    subject: v.string(),
    difficulty: v.union(v.literal("easy"), v.literal("medium"), v.literal("hard")),
    marks: v.number(),
    explanation: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    
    return await ctx.db.insert("questions", {
      ...args,
      createdAt: now,
    });
  },
});

// Seed sample questions for an examination
export const seedQuestionsForExam = mutation({
  args: { examinationId: v.id("examinations") },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    
    // Sample questions for JKSSB/JKPSC exams
    const sampleQuestions = [
      {
        questionText: "What is the capital of Jammu and Kashmir?",
        options: [
          { text: "Jammu", isCorrect: false },
          { text: "Srinagar", isCorrect: true },
          { text: "Leh", isCorrect: false },
          { text: "Kargil", isCorrect: false }
        ],
        subject: "General Knowledge",
        difficulty: "easy",
        marks: 1,
        explanation: "Srinagar is the summer capital of Jammu and Kashmir."
      },
      {
        questionText: "Which river is known as the lifeline of Kashmir?",
        options: [
          { text: "Chenab", isCorrect: false },
          { text: "Jhelum", isCorrect: true },
          { text: "Ravi", isCorrect: false },
          { text: "Indus", isCorrect: false }
        ],
        subject: "Geography",
        difficulty: "medium",
        marks: 1,
        explanation: "The Jhelum River flows through the Kashmir Valley and is considered its lifeline."
      },
      {
        questionText: "Who was the first Chief Minister of Jammu and Kashmir?",
        options: [
          { text: "Sheikh Abdullah", isCorrect: true },
          { text: "Bakshi Ghulam Mohammad", isCorrect: false },
          { text: "G.M. Sadiq", isCorrect: false },
          { text: "Farooq Abdullah", isCorrect: false }
        ],
        subject: "History",
        difficulty: "medium",
        marks: 1,
        explanation: "Sheikh Abdullah was the first Chief Minister of Jammu and Kashmir."
      },
      {
        questionText: "What is 15% of 200?",
        options: [
          { text: "25", isCorrect: false },
          { text: "30", isCorrect: true },
          { text: "35", isCorrect: false },
          { text: "40", isCorrect: false }
        ],
        subject: "Mathematics",
        difficulty: "easy",
        marks: 1,
        explanation: "15% of 200 = (15/100) × 200 = 30"
      },
      {
        questionText: "Which Article of the Indian Constitution gave special status to Jammu and Kashmir?",
        options: [
          { text: "Article 370", isCorrect: true },
          { text: "Article 371", isCorrect: false },
          { text: "Article 356", isCorrect: false },
          { text: "Article 360", isCorrect: false }
        ],
        subject: "Indian Polity",
        difficulty: "medium",
        marks: 1,
        explanation: "Article 370 gave special autonomous status to Jammu and Kashmir (now abrogated)."
      },
      {
        questionText: "What is the synonym of 'Abundant'?",
        options: [
          { text: "Scarce", isCorrect: false },
          { text: "Plentiful", isCorrect: true },
          { text: "Limited", isCorrect: false },
          { text: "Rare", isCorrect: false }
        ],
        subject: "English",
        difficulty: "easy",
        marks: 1,
        explanation: "Abundant means existing in large quantities; plentiful."
      },
      {
        questionText: "Which is the largest district in Jammu and Kashmir by area?",
        options: [
          { text: "Leh", isCorrect: true },
          { text: "Kargil", isCorrect: false },
          { text: "Jammu", isCorrect: false },
          { text: "Srinagar", isCorrect: false }
        ],
        subject: "Geography",
        difficulty: "medium",
        marks: 1,
        explanation: "Leh is the largest district in Jammu and Kashmir by area."
      },
      {
        questionText: "What is the square root of 144?",
        options: [
          { text: "10", isCorrect: false },
          { text: "11", isCorrect: false },
          { text: "12", isCorrect: true },
          { text: "13", isCorrect: false }
        ],
        subject: "Mathematics",
        difficulty: "easy",
        marks: 1,
        explanation: "√144 = 12 because 12 × 12 = 144"
      },
      {
        questionText: "Which festival is known as the 'Festival of Colors'?",
        options: [
          { text: "Diwali", isCorrect: false },
          { text: "Holi", isCorrect: true },
          { text: "Eid", isCorrect: false },
          { text: "Christmas", isCorrect: false }
        ],
        subject: "General Knowledge",
        difficulty: "easy",
        marks: 1,
        explanation: "Holi is known as the Festival of Colors."
      },
      {
        questionText: "What does 'CPU' stand for in computer terminology?",
        options: [
          { text: "Central Processing Unit", isCorrect: true },
          { text: "Computer Processing Unit", isCorrect: false },
          { text: "Central Program Unit", isCorrect: false },
          { text: "Computer Program Unit", isCorrect: false }
        ],
        subject: "Computer Knowledge",
        difficulty: "easy",
        marks: 1,
        explanation: "CPU stands for Central Processing Unit."
      }
    ];

    const insertedQuestions = [];
    
    for (const question of sampleQuestions) {
      const questionId = await ctx.db.insert("questions", {
        ...question,
        examinationId: args.examinationId,
        createdAt: now,
      });
      insertedQuestions.push(questionId);
    }
    
    return {
      message: "Sample questions seeded successfully",
      insertedCount: insertedQuestions.length,
      questionIds: insertedQuestions,
    };
  },
});

// Get questions by subject
export const getQuestionsBySubject = query({
  args: { 
    examinationId: v.id("examinations"),
    subject: v.string() 
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("questions")
      .withIndex("by_examination", (q) => q.eq("examinationId", args.examinationId))
      .filter((q) => q.eq(q.field("subject"), args.subject))
      .collect();
  },
});

// Delete a question
export const deleteQuestion = mutation({
  args: { id: v.id("questions") },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.id);
  },
});

// Update a question
export const updateQuestion = mutation({
  args: {
    id: v.id("questions"),
    questionText: v.optional(v.string()),
    options: v.optional(v.array(v.object({
      text: v.string(),
      isCorrect: v.boolean(),
    }))),
    subject: v.optional(v.string()),
    difficulty: v.optional(v.union(v.literal("easy"), v.literal("medium"), v.literal("hard"))),
    marks: v.optional(v.number()),
    explanation: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    
    // Remove undefined values
    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );
    
    return await ctx.db.patch(id, cleanUpdates);
  },
});
