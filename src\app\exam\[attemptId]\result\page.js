"use client";

import { useQuery } from "convex/react";
import { api } from "../../../../../convex/_generated/api";
import { useParams } from "next/navigation";
import Header from "../../../components/Header";
import Footer from "../../../components/Footer";

export default function ExamResultPage() {
  const params = useParams();
  const attemptId = params.attemptId;

  const attempt = useQuery(api.examAttempts.getExamAttempt, 
    attemptId ? { attemptId } : "skip"
  );
  
  const responses = useQuery(api.examResponses.getAttemptResponses,
    attemptId ? { attemptId } : "skip"
  );

  const statistics = useQuery(api.examResponses.getResponseStatistics,
    attemptId ? { attemptId } : "skip"
  );

  if (!attempt || !responses || !statistics) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading results...</p>
        </div>
      </div>
    );
  }

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const getGrade = (percentage) => {
    if (percentage >= 90) return { grade: 'A+', color: 'text-green-600' };
    if (percentage >= 80) return { grade: 'A', color: 'text-green-500' };
    if (percentage >= 70) return { grade: 'B+', color: 'text-blue-600' };
    if (percentage >= 60) return { grade: 'B', color: 'text-blue-500' };
    if (percentage >= 50) return { grade: 'C', color: 'text-yellow-600' };
    if (percentage >= 40) return { grade: 'D', color: 'text-orange-600' };
    return { grade: 'F', color: 'text-red-600' };
  };

  const gradeInfo = getGrade(attempt.percentage);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-lg p-8 mb-8 text-center">
            <h1 className="text-3xl font-bold mb-2">Exam Results</h1>
            <p className="text-xl opacity-90">Your performance summary</p>
          </div>

          {/* Overall Score */}
          <div className="bg-white rounded-lg shadow-md p-8 mb-8">
            <div className="text-center">
              <div className="mb-6">
                <div className={`text-6xl font-bold ${gradeInfo.color} mb-2`}>
                  {attempt.percentage.toFixed(1)}%
                </div>
                <div className={`text-2xl font-semibold ${gradeInfo.color}`}>
                  Grade: {gradeInfo.grade}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{attempt.obtainedMarks}</div>
                  <div className="text-sm text-gray-600">Marks Obtained</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-800">{attempt.totalMarks}</div>
                  <div className="text-sm text-gray-600">Total Marks</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{attempt.correctAnswers}</div>
                  <div className="text-sm text-gray-600">Correct Answers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{attempt.wrongAnswers}</div>
                  <div className="text-sm text-gray-600">Wrong Answers</div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Detailed Statistics */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-6">Detailed Statistics</h2>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-gray-600">Total Questions</span>
                  <span className="font-semibold">{statistics.totalQuestions}</span>
                </div>
                
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-gray-600">Attempted Questions</span>
                  <span className="font-semibold text-blue-600">{statistics.attemptedQuestions}</span>
                </div>
                
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-gray-600">Unanswered Questions</span>
                  <span className="font-semibold text-yellow-600">{statistics.unansweredQuestions}</span>
                </div>
                
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-gray-600">Correct Answers</span>
                  <span className="font-semibold text-green-600">{statistics.correctAnswers}</span>
                </div>
                
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-gray-600">Wrong Answers</span>
                  <span className="font-semibold text-red-600">{statistics.wrongAnswers}</span>
                </div>
                
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-gray-600">Total Time Spent</span>
                  <span className="font-semibold">{formatTime(statistics.totalTimeSpent)}</span>
                </div>
                
                <div className="flex justify-between items-center py-2">
                  <span className="text-gray-600">Average Time per Question</span>
                  <span className="font-semibold">{formatTime(Math.round(statistics.averageTimePerQuestion))}</span>
                </div>
              </div>
            </div>

            {/* Performance Analysis */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-6">Performance Analysis</h2>
              
              <div className="space-y-6">
                {/* Accuracy */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600">Accuracy</span>
                    <span className="font-semibold">
                      {statistics.attemptedQuestions > 0 
                        ? ((statistics.correctAnswers / statistics.attemptedQuestions) * 100).toFixed(1)
                        : 0}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ 
                        width: `${statistics.attemptedQuestions > 0 
                          ? (statistics.correctAnswers / statistics.attemptedQuestions) * 100 
                          : 0}%` 
                      }}
                    ></div>
                  </div>
                </div>

                {/* Completion Rate */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600">Completion Rate</span>
                    <span className="font-semibold">
                      {((statistics.attemptedQuestions / statistics.totalQuestions) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ 
                        width: `${(statistics.attemptedQuestions / statistics.totalQuestions) * 100}%` 
                      }}
                    ></div>
                  </div>
                </div>

                {/* Score Distribution */}
                <div className="mt-6">
                  <h3 className="text-lg font-medium text-gray-800 mb-4">Score Breakdown</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-green-500 rounded mr-3"></div>
                      <span className="text-sm">Correct: {statistics.correctAnswers} questions</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-red-500 rounded mr-3"></div>
                      <span className="text-sm">Wrong: {statistics.wrongAnswers} questions</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-gray-400 rounded mr-3"></div>
                      <span className="text-sm">Unanswered: {statistics.unansweredQuestions} questions</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 text-center space-x-4">
            <button
              onClick={() => window.print()}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Print Results
            </button>
            
            <a
              href="/dashboard"
              className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors inline-block"
            >
              Back to Dashboard
            </a>
            
            <a
              href="/examinations"
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors inline-block"
            >
              Browse More Exams
            </a>
          </div>

          {/* Exam Information */}
          <div className="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Exam Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Exam Status:</span>
                <span className="ml-2 font-medium capitalize">{attempt.status.replace('_', ' ')}</span>
              </div>
              <div>
                <span className="text-gray-600">Started At:</span>
                <span className="ml-2 font-medium">
                  {new Date(attempt.startTime).toLocaleString()}
                </span>
              </div>
              {attempt.endTime && (
                <div>
                  <span className="text-gray-600">Completed At:</span>
                  <span className="ml-2 font-medium">
                    {new Date(attempt.endTime).toLocaleString()}
                  </span>
                </div>
              )}
              {attempt.rank && (
                <div>
                  <span className="text-gray-600">Rank:</span>
                  <span className="ml-2 font-medium text-purple-600">#{attempt.rank}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
