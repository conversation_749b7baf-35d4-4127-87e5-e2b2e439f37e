import { Geist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";


const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "JKPACE - Jammu and Kashmir Portal for Aspirants of Competitive Examinations",
  description: "Online portal for JKSSB and JKPSC examination preparation and testing for aspirants in Jammu and Kashmir",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
      
          {children}

      </body>
    </html>
  );
}
