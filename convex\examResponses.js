import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Submit or update a response to a question
export const submitResponse = mutation({
  args: {
    attemptId: v.id("examAttempts"),
    questionId: v.id("questions"),
    selectedOption: v.number(), // index of selected option
    timeSpent: v.number(), // in seconds
  },
  handler: async (ctx, args) => {
    // Check if attempt exists and is in progress
    const attempt = await ctx.db.get(args.attemptId);
    if (!attempt) {
      throw new Error("Exam attempt not found");
    }
    if (attempt.status !== "in_progress") {
      throw new Error("Exam is not in progress");
    }

    // Get question details
    const question = await ctx.db.get(args.questionId);
    if (!question) {
      throw new Error("Question not found");
    }

    // Check if the question belongs to the same examination
    if (question.examinationId !== attempt.examinationId) {
      throw new Error("Question does not belong to this examination");
    }

    // Get examination details for marking calculation
    const examination = await ctx.db.get(attempt.examinationId);
    if (!examination) {
      throw new Error("Examination not found");
    }

    // Check if response already exists
    const existingResponse = await ctx.db
      .query("examResponses")
      .withIndex("by_attempt", (q) => q.eq("attemptId", args.attemptId))
      .filter((q) => q.eq(q.field("questionId"), args.questionId))
      .first();

    // Determine if answer is correct and calculate marks
    const selectedOptionData = question.options[args.selectedOption];
    const isCorrect = selectedOptionData ? selectedOptionData.isCorrect : false;
    
    let marksAwarded = 0;
    if (isCorrect) {
      marksAwarded = examination.marksPerQuestion;
    } else if (examination.negativeMarking) {
      marksAwarded = -(examination.negativeMarks || 0);
    }

    const now = new Date().toISOString();

    if (existingResponse) {
      // Update existing response
      return await ctx.db.patch(existingResponse._id, {
        selectedOption: args.selectedOption,
        isCorrect,
        marksAwarded,
        timeSpent: args.timeSpent,
        answeredAt: now,
      });
    } else {
      // Create new response
      return await ctx.db.insert("examResponses", {
        attemptId: args.attemptId,
        questionId: args.questionId,
        selectedOption: args.selectedOption,
        isCorrect,
        marksAwarded,
        timeSpent: args.timeSpent,
        answeredAt: now,
      });
    }
  },
});

// Clear a response (mark as unanswered)
export const clearResponse = mutation({
  args: {
    attemptId: v.id("examAttempts"),
    questionId: v.id("questions"),
  },
  handler: async (ctx, args) => {
    // Check if attempt exists and is in progress
    const attempt = await ctx.db.get(args.attemptId);
    if (!attempt) {
      throw new Error("Exam attempt not found");
    }
    if (attempt.status !== "in_progress") {
      throw new Error("Exam is not in progress");
    }

    // Find and delete the response
    const response = await ctx.db
      .query("examResponses")
      .withIndex("by_attempt", (q) => q.eq("attemptId", args.attemptId))
      .filter((q) => q.eq(q.field("questionId"), args.questionId))
      .first();

    if (response) {
      await ctx.db.delete(response._id);
      return { message: "Response cleared successfully" };
    }

    return { message: "No response found to clear" };
  },
});

// Get all responses for an exam attempt
export const getAttemptResponses = query({
  args: { attemptId: v.id("examAttempts") },
  handler: async (ctx, args) => {
    const responses = await ctx.db
      .query("examResponses")
      .withIndex("by_attempt", (q) => q.eq("attemptId", args.attemptId))
      .collect();

    // Get question details for each response
    const responsesWithQuestions = await Promise.all(
      responses.map(async (response) => {
        const question = await ctx.db.get(response.questionId);
        return {
          ...response,
          question,
        };
      })
    );

    return responsesWithQuestions;
  },
});

// Get response for a specific question in an attempt
export const getQuestionResponse = query({
  args: {
    attemptId: v.id("examAttempts"),
    questionId: v.id("questions"),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("examResponses")
      .withIndex("by_attempt", (q) => q.eq("attemptId", args.attemptId))
      .filter((q) => q.eq(q.field("questionId"), args.questionId))
      .first();
  },
});

// Get response statistics for an attempt
export const getResponseStatistics = query({
  args: { attemptId: v.id("examAttempts") },
  handler: async (ctx, args) => {
    const responses = await ctx.db
      .query("examResponses")
      .withIndex("by_attempt", (q) => q.eq("attemptId", args.attemptId))
      .collect();

    const attempt = await ctx.db.get(args.attemptId);
    if (!attempt) {
      throw new Error("Exam attempt not found");
    }

    const totalQuestions = attempt.totalQuestions;
    const attemptedQuestions = responses.length;
    const unansweredQuestions = totalQuestions - attemptedQuestions;
    
    const correctAnswers = responses.filter(r => r.isCorrect === true).length;
    const wrongAnswers = responses.filter(r => r.isCorrect === false).length;
    
    const totalMarksAwarded = responses.reduce((sum, r) => sum + r.marksAwarded, 0);
    const totalTimeSpent = responses.reduce((sum, r) => sum + r.timeSpent, 0);

    return {
      totalQuestions,
      attemptedQuestions,
      unansweredQuestions,
      correctAnswers,
      wrongAnswers,
      totalMarksAwarded,
      totalTimeSpent,
      averageTimePerQuestion: attemptedQuestions > 0 ? totalTimeSpent / attemptedQuestions : 0,
    };
  },
});

// Get responses by question (for analysis)
export const getQuestionResponses = query({
  args: { questionId: v.id("questions") },
  handler: async (ctx, args) => {
    const responses = await ctx.db
      .query("examResponses")
      .withIndex("by_question", (q) => q.eq("questionId", args.questionId))
      .collect();

    // Get attempt details for each response
    const responsesWithAttempts = await Promise.all(
      responses.map(async (response) => {
        const attempt = await ctx.db.get(response.attemptId);
        const aspirant = attempt ? await ctx.db.get(attempt.aspirantId) : null;
        return {
          ...response,
          attempt,
          aspirant,
        };
      })
    );

    return responsesWithAttempts;
  },
});

// Bulk submit responses (for auto-save functionality)
export const bulkSubmitResponses = mutation({
  args: {
    attemptId: v.id("examAttempts"),
    responses: v.array(v.object({
      questionId: v.id("questions"),
      selectedOption: v.number(),
      timeSpent: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    // Check if attempt exists and is in progress
    const attempt = await ctx.db.get(args.attemptId);
    if (!attempt) {
      throw new Error("Exam attempt not found");
    }
    if (attempt.status !== "in_progress") {
      throw new Error("Exam is not in progress");
    }

    const results = [];
    
    for (const responseData of args.responses) {
      try {
        const result = await ctx.runMutation("examResponses:submitResponse", {
          attemptId: args.attemptId,
          questionId: responseData.questionId,
          selectedOption: responseData.selectedOption,
          timeSpent: responseData.timeSpent,
        });
        results.push({ success: true, questionId: responseData.questionId, result });
      } catch (error) {
        results.push({ 
          success: false, 
          questionId: responseData.questionId, 
          error: error.message 
        });
      }
    }

    return {
      message: "Bulk response submission completed",
      results,
      successCount: results.filter(r => r.success).length,
      errorCount: results.filter(r => !r.success).length,
    };
  },
});
