export default function AboutPage() {
  return (
    <div className="bg-gray-50">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">About JKPACE</h1>
            <p className="text-xl text-gray-600">
              Jammu and Kashmir Portal for Aspirants of Competitive Examinations
            </p>
          </div>

          {/* Mission */}
          <div className="bg-white rounded-lg shadow-md p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Our Mission</h2>
            <p className="text-gray-600 leading-relaxed">
              JKPACE is dedicated to providing a comprehensive, transparent, and efficient platform for 
              aspirants seeking to appear in competitive examinations conducted by JKSSB (Jammu and Kashmir 
              Services Selection Board) and JKPSC (Jammu and Kashmir Public Service Commission). Our mission 
              is to streamline the examination process, making it accessible and user-friendly for all candidates 
              across the Union Territory of Jammu and Kashmir.
            </p>
          </div>

          {/* Features */}
          <div className="bg-white rounded-lg shadow-md p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6">Key Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-start space-x-3">
                <div className="bg-blue-100 p-2 rounded-lg">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">Online Registration</h3>
                  <p className="text-gray-600 text-sm">Easy and secure online registration for all examinations</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="bg-green-100 p-2 rounded-lg">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">Secure Payments</h3>
                  <p className="text-gray-600 text-sm">Safe and reliable payment gateway for application fees</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="bg-purple-100 p-2 rounded-lg">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">Online Testing</h3>
                  <p className="text-gray-600 text-sm">Computer-based testing with instant results</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="bg-yellow-100 p-2 rounded-lg">
                  <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">Study Resources</h3>
                  <p className="text-gray-600 text-sm">Comprehensive study materials and practice tests</p>
                </div>
              </div>
            </div>
          </div>

          {/* Services */}
          <div className="bg-white rounded-lg shadow-md p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6">Our Services</h2>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-semibold text-gray-800">JKSSB Examinations</h3>
                <p className="text-gray-600 text-sm">
                  Complete management of JKSSB examinations including Class IV, Non-Gazetted, and other recruitment drives.
                </p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-semibold text-gray-800">JKPSC Examinations</h3>
                <p className="text-gray-600 text-sm">
                  Comprehensive support for JKPSC examinations including KAS, Teaching positions, and other gazetted posts.
                </p>
              </div>
              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-semibold text-gray-800">Mock Tests</h3>
                <p className="text-gray-600 text-sm">
                  Practice examinations to help candidates prepare effectively for their competitive exams.
                </p>
              </div>
              <div className="border-l-4 border-yellow-500 pl-4">
                <h3 className="font-semibold text-gray-800">Result Management</h3>
                <p className="text-gray-600 text-sm">
                  Transparent and timely declaration of results with detailed performance analysis.
                </p>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white rounded-lg shadow-md p-8">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6">Contact Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">Technical Support</h3>
                <p className="text-gray-600 text-sm mb-1">Email: <EMAIL></p>
                <p className="text-gray-600 text-sm">Phone: 1800-XXX-XXXX</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">Exam Queries</h3>
                <p className="text-gray-600 text-sm mb-1">Email: <EMAIL></p>
                <p className="text-gray-600 text-sm">Phone: 1800-YYY-YYYY</p>
              </div>
            </div>
            
            <div className="mt-6 pt-6 border-t">
              <p className="text-gray-600 text-sm">
                <strong>Office Address:</strong><br />
                Jammu and Kashmir Portal for Aspirants of Competitive Examinations<br />
                Government of Jammu and Kashmir<br />
                Srinagar/Jammu, J&K, India
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
