import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Examinations table - stores JKSSB and JKPSC exam details
  examinations: defineTable({
    title: v.string(),
    description: v.string(),
    examType: v.union(v.literal("JKSSB"), v.literal("JKPSC")),
    category: v.string(), // e.g., "Class IV", "Gazetted", "Non-Gazetted"
    totalSeats: v.number(),
    availableSeats: v.number(),
    registrationStartDate: v.string(),
    registrationEndDate: v.string(),
    examDate: v.string(),
    examTime: v.string(),
    duration: v.number(), // in minutes
    totalQuestions: v.number(),
    marksPerQuestion: v.number(),
    negativeMarking: v.boolean(),
    negativeMarks: v.optional(v.number()),
    syllabus: v.array(v.string()),
    eligibilityCriteria: v.array(v.string()),
    applicationFee: v.number(),
    status: v.union(
      v.literal("upcoming"),
      v.literal("registration_open"),
      v.literal("registration_closed"),
      v.literal("exam_completed"),
      v.literal("results_declared")
    ),
    createdAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_exam_type", ["examType"])
    .index("by_status", ["status"])
    .index("by_exam_date", ["examDate"]),

  // Aspirants table - stores user registration data
  aspirants: defineTable({
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    phone: v.string(),
    dateOfBirth: v.string(),
    gender: v.union(v.literal("male"), v.literal("female"), v.literal("other")),
    category: v.union(
      v.literal("General"),
      v.literal("OBC"),
      v.literal("SC"),
      v.literal("ST"),
      v.literal("EWS")
    ),
    address: v.object({
      street: v.string(),
      city: v.string(),
      district: v.string(),
      state: v.string(),
      pincode: v.string(),
    }),
    qualification: v.object({
      degree: v.string(),
      university: v.string(),
      percentage: v.number(),
      yearOfPassing: v.number(),
    }),
    registeredAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_email", ["email"])
    .index("by_phone", ["phone"]),

  // Exam Registrations table - tracks which aspirant registered for which exam
  examRegistrations: defineTable({
    aspirantId: v.id("aspirants"),
    examinationId: v.id("examinations"),
    registrationNumber: v.string(),
    applicationStatus: v.union(
      v.literal("submitted"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected")
    ),
    paymentStatus: v.union(
      v.literal("pending"),
      v.literal("completed"),
      v.literal("failed")
    ),
    paymentId: v.optional(v.string()),
    admitCardGenerated: v.boolean(),
    examCenter: v.optional(v.string()),
    seatNumber: v.optional(v.string()),
    registeredAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_aspirant", ["aspirantId"])
    .index("by_examination", ["examinationId"])
    .index("by_registration_number", ["registrationNumber"]),

  // Questions table - stores exam questions
  questions: defineTable({
    examinationId: v.id("examinations"),
    questionText: v.string(),
    options: v.array(v.object({
      text: v.string(),
      isCorrect: v.boolean(),
    })),
    subject: v.string(),
    difficulty: v.union(v.literal("easy"), v.literal("medium"), v.literal("hard")),
    marks: v.number(),
    explanation: v.optional(v.string()),
    createdAt: v.string(),
  })
    .index("by_examination", ["examinationId"])
    .index("by_subject", ["subject"]),

  // Exam Attempts table - tracks when aspirants take exams
  examAttempts: defineTable({
    aspirantId: v.id("aspirants"),
    examinationId: v.id("examinations"),
    registrationId: v.id("examRegistrations"),
    startTime: v.string(),
    endTime: v.optional(v.string()),
    status: v.union(
      v.literal("in_progress"),
      v.literal("completed"),
      v.literal("abandoned"),
      v.literal("time_up")
    ),
    totalScore: v.optional(v.number()),
    correctAnswers: v.optional(v.number()),
    wrongAnswers: v.optional(v.number()),
    unanswered: v.optional(v.number()),
    percentile: v.optional(v.number()),
    rank: v.optional(v.number()),
    createdAt: v.string(),
  })
    .index("by_aspirant", ["aspirantId"])
    .index("by_examination", ["examinationId"])
    .index("by_registration", ["registrationId"]),

  // Exam Responses table - stores individual question responses
  examResponses: defineTable({
    attemptId: v.id("examAttempts"),
    questionId: v.id("questions"),
    selectedOption: v.optional(v.number()), // index of selected option
    isCorrect: v.optional(v.boolean()),
    marksAwarded: v.optional(v.number()),
    timeSpent: v.optional(v.number()), // in seconds
    answeredAt: v.optional(v.string()),
  })
    .index("by_attempt", ["attemptId"])
    .index("by_question", ["questionId"]),
});
