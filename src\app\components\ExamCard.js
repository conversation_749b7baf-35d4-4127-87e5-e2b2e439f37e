"use client";

import Link from "next/link";

export default function ExamCard({ exam }) {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'registration_open':
        return 'bg-green-100 text-green-800';
      case 'upcoming':
        return 'bg-blue-100 text-blue-800';
      case 'registration_closed':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'registration_open':
        return 'Registration Open';
      case 'upcoming':
        return 'Upcoming';
      case 'registration_closed':
        return 'Registration Closed';
      case 'exam_completed':
        return 'Exam Completed';
      case 'results_declared':
        return 'Results Declared';
      default:
        return status;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-4">
        <div className="flex justify-between items-start mb-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(exam.status)}`}>
            {getStatusText(exam.status)}
          </span>
          <span className="bg-white bg-opacity-20 px-2 py-1 rounded text-xs font-medium">
            {exam.examType}
          </span>
        </div>
        <h3 className="text-lg font-bold leading-tight">{exam.title}</h3>
      </div>

      {/* Content */}
      <div className="p-4">
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {exam.description}
        </p>

        {/* Key Details */}
        <div className="space-y-2 mb-4">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-500">Category:</span>
            <span className="font-medium text-gray-800">{exam.category}</span>
          </div>
          
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-500">Total Seats:</span>
            <span className="font-medium text-gray-800">{exam.totalSeats}</span>
          </div>
          
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-500">Available:</span>
            <span className="font-medium text-green-600">{exam.availableSeats}</span>
          </div>
          
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-500">Application Fee:</span>
            <span className="font-medium text-gray-800">₹{exam.applicationFee}</span>
          </div>
        </div>

        {/* Important Dates */}
        <div className="bg-gray-50 rounded-lg p-3 mb-4">
          <h4 className="text-sm font-semibold text-gray-800 mb-2">Important Dates</h4>
          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-600">Registration Ends:</span>
              <span className="font-medium">{formatDate(exam.registrationEndDate)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Exam Date:</span>
              <span className="font-medium">{formatDate(exam.examDate)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Exam Time:</span>
              <span className="font-medium">{exam.examTime}</span>
            </div>
          </div>
        </div>

        {/* Exam Details */}
        <div className="grid grid-cols-2 gap-4 mb-4 text-xs">
          <div className="text-center bg-blue-50 rounded p-2">
            <div className="font-semibold text-blue-800">{exam.totalQuestions}</div>
            <div className="text-gray-600">Questions</div>
          </div>
          <div className="text-center bg-green-50 rounded p-2">
            <div className="font-semibold text-green-800">{exam.duration} min</div>
            <div className="text-gray-600">Duration</div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Link
            href={`/examinations/${exam._id}`}
            className="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            View Details
          </Link>
          {exam.status === 'registration_open' && (
            <Link
              href={`/examinations/${exam._id}/register`}
              className="flex-1 bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
            >
              Register Now
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}
