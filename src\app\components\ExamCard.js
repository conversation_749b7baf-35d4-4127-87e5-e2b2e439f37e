"use client";

import Link from "next/link";

export default function ExamCard({ exam }) {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'registration_open':
        return 'bg-green-100 text-green-800 border-green-300';
      case 'upcoming':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'registration_closed':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'registration_open':
        return 'Registration Open';
      case 'upcoming':
        return 'Upcoming';
      case 'registration_closed':
        return 'Registration Closed';
      default:
        return status;
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100">
      {/* Header with Exam Name and Registration Fee */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white p-6">
        <div className="flex justify-between items-start mb-3">
          <span className={`px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(exam.status)}`}>
            {getStatusText(exam.status)}
          </span>
        </div>
        <h3 className="text-xl font-bold leading-tight mb-2">
          {exam.title}
          <sup className="text-sm font-normal ml-2 bg-white bg-opacity-20 px-3 py-1 rounded-full">
            ₹{exam.applicationFee}
          </sup>
        </h3>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Conducting Body and Date/Time Row */}
        <div className="flex items-center justify-between mb-6 p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-200">
          <div className="flex items-center space-x-4">
            {/* Conducting Body Logo */}
            <div className="w-14 h-14 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-xl flex items-center justify-center text-white font-bold text-sm shadow-md">
              {exam.examType}
            </div>
            <div>
              <div className="text-sm font-bold text-gray-800">{exam.examType}</div>
              <div className="text-xs text-gray-600 font-medium">{exam.category}</div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm font-bold text-gray-800">{formatDate(exam.examDate)}</div>
            <div className="text-xs text-gray-600 font-medium">{exam.examTime}</div>
          </div>
        </div>

        <p className="text-gray-600 text-sm mb-6 line-clamp-2 leading-relaxed">
          {exam.description}
        </p>

        {/* Key Details */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center bg-blue-50 rounded-lg p-3 border border-blue-200">
            <div className="font-bold text-blue-800 text-lg">{exam.totalSeats}</div>
            <div className="text-gray-600 text-xs font-medium">Total Seats</div>
          </div>
          <div className="text-center bg-green-50 rounded-lg p-3 border border-green-200">
            <div className="font-bold text-green-800 text-lg">{exam.availableSeats}</div>
            <div className="text-gray-600 text-xs font-medium">Available</div>
          </div>
        </div>

        {/* Registration Deadline */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex justify-between items-center">
            <span className="text-sm font-semibold text-yellow-800">Registration Ends:</span>
            <span className="text-sm font-bold text-yellow-900">{formatDate(exam.registrationEndDate)}</span>
          </div>
        </div>

        {/* Register Now Button */}
        {exam.status === 'registration_open' ? (
          <Link
            href={`/examinations/${exam._id}/register`}
            className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white text-center py-4 px-6 rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-300 font-bold text-sm block shadow-md hover:shadow-lg transform hover:scale-105"
          >
            Register Now
          </Link>
        ) : (
          <div className="flex gap-3">
            <Link
              href={`/examinations/${exam._id}`}
              className="flex-1 bg-blue-600 text-white text-center py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm font-semibold"
            >
              View Details
            </Link>
            <button
              disabled
              className="flex-1 bg-gray-400 text-white text-center py-3 px-4 rounded-lg cursor-not-allowed text-sm font-semibold"
            >
              {exam.status === 'registration_closed' ? 'Registration Closed' : 'Not Available'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
