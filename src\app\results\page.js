"use client";

import Header from "../components/Header";
import Footer from "../components/Footer";

export default function ResultsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <div className="bg-white rounded-lg shadow-md p-8">
            <svg className="w-16 h-16 text-blue-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            
            <h1 className="text-3xl font-bold text-gray-800 mb-4">
              Exam Results
            </h1>
            
            <p className="text-gray-600 mb-8">
              View your examination results and performance analysis. Results are typically published within 24-48 hours after exam completion.
            </p>

            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-semibold text-blue-800 mb-2">How to Check Results</h3>
                <ol className="text-sm text-blue-700 text-left space-y-1">
                  <li>1. Login to your account</li>
                  <li>2. Go to your dashboard</li>
                  <li>3. Click on "View Results" for completed exams</li>
                  <li>4. Download your result certificate</li>
                </ol>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 className="font-semibold text-yellow-800 mb-2">Result Features</h3>
                <ul className="text-sm text-yellow-700 text-left space-y-1">
                  <li>• Detailed score breakdown</li>
                  <li>• Subject-wise performance analysis</li>
                  <li>• Rank and percentile information</li>
                  <li>• Time management statistics</li>
                  <li>• Downloadable certificates</li>
                </ul>
              </div>
            </div>

            <div className="mt-8 space-x-4">
              <a
                href="/login"
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-block"
              >
                Login to View Results
              </a>
              
              <a
                href="/dashboard"
                className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors inline-block"
              >
                Go to Dashboard
              </a>
            </div>

            <div className="mt-8 pt-6 border-t text-sm text-gray-500">
              <p>
                For any queries regarding results, contact us at{" "}
                <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
