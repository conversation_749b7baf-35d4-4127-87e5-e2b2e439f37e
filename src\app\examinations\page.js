"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import Header from "../components/Header";
import ExamCard from "../components/ExamCard";
import Footer from "../components/Footer";
import LoadingSpinner from "../components/LoadingSpinner";

export default function ExaminationsPage() {
  const [filters, setFilters] = useState({
    examType: "",
    status: "",
  });

  const examinations = useQuery(api.examinations.getExaminations, filters);

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value === "all" ? "" : value
    }));
  };

  if (examinations === undefined) {
    return <LoadingSpinner />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Page Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold mb-2">All Examinations</h1>
          <p className="text-xl opacity-90">
            Browse and register for JKSSB and JKPSC examinations
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Filter Examinations</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Exam Type
              </label>
              <select
                value={filters.examType || "all"}
                onChange={(e) => handleFilterChange("examType", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Types</option>
                <option value="JKSSB">JKSSB</option>
                <option value="JKPSC">JKPSC</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={filters.status || "all"}
                onChange={(e) => handleFilterChange("status", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="upcoming">Upcoming</option>
                <option value="registration_open">Registration Open</option>
                <option value="registration_closed">Registration Closed</option>
                <option value="exam_completed">Exam Completed</option>
                <option value="results_declared">Results Declared</option>
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={() => setFilters({ examType: "", status: "" })}
                className="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Results Summary */}
        <div className="mb-6">
          <p className="text-gray-600">
            Showing {examinations.length} examination{examinations.length !== 1 ? 's' : ''}
            {filters.examType && ` for ${filters.examType}`}
            {filters.status && ` with status: ${filters.status.replace('_', ' ')}`}
          </p>
        </div>

        {/* Examinations Grid */}
        {examinations.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-white rounded-lg shadow-md p-8">
              <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                No Examinations Found
              </h3>
              <p className="text-gray-600 mb-4">
                No examinations match your current filter criteria.
              </p>
              <button
                onClick={() => setFilters({ examType: "", status: "" })}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                View All Examinations
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {examinations.map((exam) => (
              <ExamCard key={exam._id} exam={exam} />
            ))}
          </div>
        )}

        {/* Statistics */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Quick Statistics</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {examinations.filter(e => e.examType === 'JKSSB').length}
              </div>
              <div className="text-sm text-gray-600">JKSSB Exams</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-600">
                {examinations.filter(e => e.examType === 'JKPSC').length}
              </div>
              <div className="text-sm text-gray-600">JKPSC Exams</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {examinations.filter(e => e.status === 'registration_open').length}
              </div>
              <div className="text-sm text-gray-600">Open for Registration</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {examinations.reduce((sum, e) => sum + e.totalSeats, 0)}
              </div>
              <div className="text-sm text-gray-600">Total Seats</div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
