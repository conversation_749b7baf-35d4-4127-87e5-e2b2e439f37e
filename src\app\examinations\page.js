"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import ExamCard from "../components/ExamCard";
import LoadingSpinner from "../components/LoadingSpinner";

export default function ExaminationsPage() {
  const [filters, setFilters] = useState({
    examType: "",
    status: "",
  });

  const examinations = useQuery(api.examinations.getExaminations, filters);

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value === "all" ? "" : value
    }));
  };

  if (examinations === undefined) {
    return <LoadingSpinner />;
  }

  return (
    <div className="bg-gray-50">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">All Examinations</h1>
          <p className="text-xl opacity-90 max-w-2xl">
            Browse and register for JKSSB and JKPSC examinations. Find the perfect opportunity to serve Jammu and Kashmir.
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Filter Examinations</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Exam Type
              </label>
              <select
                value={filters.examType}
                onChange={(e) => handleFilterChange('examType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Types</option>
                <option value="JKSSB">JKSSB</option>
                <option value="JKPSC">JKPSC</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Status</option>
                <option value="registration_open">Registration Open</option>
                <option value="upcoming">Upcoming</option>
                <option value="registration_closed">Registration Closed</option>
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={() => setFilters({ examType: "", status: "" })}
                className="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Examinations Grid */}
        {examinations.length === 0 ? (
          <div className="text-center py-16">
            <div className="bg-white rounded-lg shadow-md p-12 max-w-md mx-auto">
              <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">No Examinations Found</h3>
              <p className="text-gray-600 mb-6">
                No examinations match your current filters. Try adjusting your search criteria.
              </p>
              <button
                onClick={() => setFilters({ examType: "", status: "" })}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
              {examinations.map((exam) => (
                <ExamCard key={exam._id} exam={exam} />
              ))}
            </div>

            {/* Statistics */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-6">Examination Statistics</h2>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {examinations.length}
                  </div>
                  <div className="text-sm text-gray-600">Total Examinations</div>
                </div>
                
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {examinations.filter(e => e.status === 'registration_open').length}
                  </div>
                  <div className="text-sm text-gray-600">Registration Open</div>
                </div>
                
                <div className="text-center p-4 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">
                    {examinations.filter(e => e.examType === 'JKSSB').length}
                  </div>
                  <div className="text-sm text-gray-600">JKSSB Exams</div>
                </div>
                
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {examinations.reduce((sum, e) => sum + e.totalSeats, 0)}
                  </div>
                  <div className="text-sm text-gray-600">Total Seats</div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
