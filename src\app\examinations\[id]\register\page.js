"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../../convex/_generated/api";
import { useParams, useRouter } from "next/navigation";
import Header from "../../../components/Header";
import Footer from "../../../components/Footer";
import LoadingSpinner from "../../../components/LoadingSpinner";

export default function ExamRegistrationPage() {
  const params = useParams();
  const router = useRouter();
  const examId = params.id;

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState({ type: "", text: "" });

  const examination = useQuery(api.examinations.getExaminationById, 
    examId ? { id: examId } : "skip"
  );

  const registerForExam = useMutation(api.examRegistrations.registerForExam);

  const handleRegistration = async () => {
    if (!examination) return;

    // For demo purposes, we'll use a dummy aspirant ID
    // In a real app, this would come from the authenticated user
    const dummyAspirantId = "dummy_aspirant_id";

    setIsSubmitting(true);
    setMessage({ type: "", text: "" });

    try {
      await registerForExam({
        aspirantId: dummyAspirantId,
        examinationId: examId,
      });

      setMessage({
        type: "success",
        text: "Registration successful! You will receive a confirmation email shortly."
      });

      // Redirect to dashboard after 3 seconds
      setTimeout(() => {
        router.push("/dashboard");
      }, 3000);

    } catch (error) {
      setMessage({
        type: "error",
        text: error.message || "Registration failed. Please try again."
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (examination === undefined) {
    return <LoadingSpinner />;
  }

  if (examination === null) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              Examination Not Found
            </h1>
            <p className="text-gray-600 mb-8">
              The examination you're trying to register for doesn't exist.
            </p>
            <a
              href="/examinations"
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Browse All Examinations
            </a>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-lg p-8 mb-8">
            <h1 className="text-3xl font-bold mb-2">Register for Examination</h1>
            <p className="text-xl opacity-90">{examination.title}</p>
          </div>

          {message.text && (
            <div className={`mb-6 p-4 rounded-lg ${
              message.type === 'success' 
                ? 'bg-green-100 text-green-700 border border-green-300' 
                : 'bg-red-100 text-red-700 border border-red-300'
            }`}>
              {message.text}
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Exam Summary */}
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Examination Summary</h2>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Exam Type:</span>
                    <span className="font-medium">{examination.examType}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Category:</span>
                    <span className="font-medium">{examination.category}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Questions:</span>
                    <span className="font-medium">{examination.totalQuestions}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Duration:</span>
                    <span className="font-medium">{examination.duration} minutes</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Application Fee:</span>
                    <span className="font-medium text-green-600">₹{examination.applicationFee}</span>
                  </div>
                </div>
              </div>

              {/* Registration Requirements */}
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Registration Requirements</h2>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                  <h3 className="font-semibold text-yellow-800 mb-2">Important Note</h3>
                  <p className="text-yellow-700 text-sm">
                    This is a demo version. In the actual portal, you would need to:
                  </p>
                </div>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>Complete your profile with all required documents</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>Upload photograph and signature</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>Pay the application fee online</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>Meet all eligibility criteria</span>
                  </li>
                </ul>
              </div>

              {/* Terms and Conditions */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Terms and Conditions</h2>
                <div className="text-sm text-gray-600 space-y-2">
                  <p>• Registration is subject to verification of documents and eligibility criteria.</p>
                  <p>• Application fee is non-refundable under any circumstances.</p>
                  <p>• Candidates must report to the exam center 30 minutes before the scheduled time.</p>
                  <p>• Any false information provided will lead to disqualification.</p>
                  <p>• The decision of JKSSB/JKPSC will be final in all matters.</p>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Important Dates */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Important Dates</h2>
                <div className="space-y-3">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Registration End</h3>
                    <p className="text-gray-800 font-medium">{formatDate(examination.registrationEndDate)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Exam Date</h3>
                    <p className="text-gray-800 font-medium">{formatDate(examination.examDate)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Exam Time</h3>
                    <p className="text-gray-800 font-medium">{examination.examTime}</p>
                  </div>
                </div>
              </div>

              {/* Registration Action */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Complete Registration</h2>
                
                {examination.status === 'registration_open' ? (
                  <div className="space-y-4">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <p className="text-green-700 text-sm">
                        Registration is currently open for this examination.
                      </p>
                    </div>
                    
                    <button
                      onClick={handleRegistration}
                      disabled={isSubmitting}
                      className="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
                    >
                      {isSubmitting ? "Processing..." : "Register Now"}
                    </button>
                  </div>
                ) : (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <p className="text-red-700 text-sm">
                      Registration is not currently open for this examination.
                    </p>
                  </div>
                )}

                <div className="mt-4 space-y-2">
                  <a
                    href={`/examinations/${examination._id}`}
                    className="block w-full bg-gray-600 text-white text-center py-2 rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    Back to Exam Details
                  </a>
                  <a
                    href="/examinations"
                    className="block w-full bg-blue-600 text-white text-center py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Browse All Exams
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
