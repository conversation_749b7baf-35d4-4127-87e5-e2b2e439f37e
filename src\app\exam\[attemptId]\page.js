"use client";

import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { useParams, useRouter } from "next/navigation";

export default function OnlineExamPage() {
  const params = useParams();
  const router = useRouter();
  const attemptId = params.attemptId;

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const attempt = useQuery(api.examAttempts.getExamAttempt, 
    attemptId ? { attemptId } : "skip"
  );
  
  const questions = useQuery(api.questions.getExaminationQuestions,
    attempt ? { examinationId: attempt.examinationId } : "skip"
  );

  const responses = useQuery(api.examResponses.getAttemptResponses,
    attemptId ? { attemptId } : "skip"
  );

  const submitResponse = useMutation(api.examResponses.submitResponse);
  const submitExam = useMutation(api.examAttempts.submitExamAttempt);

  // Initialize selected answers from existing responses
  useEffect(() => {
    if (responses) {
      const answersMap = {};
      responses.forEach(response => {
        answersMap[response.questionId] = response.selectedOption;
      });
      setSelectedAnswers(answersMap);
    }
  }, [responses]);

  // Timer logic
  useEffect(() => {
    if (attempt && attempt.status === "in_progress") {
      const startTime = new Date(attempt.startTime);
      const duration = attempt.examination?.duration || 90; // default 90 minutes
      const endTime = new Date(startTime.getTime() + duration * 60000);
      
      const updateTimer = () => {
        const now = new Date();
        const remaining = Math.max(0, endTime - now);
        setTimeRemaining(remaining);
        
        if (remaining === 0) {
          handleAutoSubmit();
        }
      };

      updateTimer();
      const interval = setInterval(updateTimer, 1000);
      
      return () => clearInterval(interval);
    }
  }, [attempt]);

  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleAnswerSelect = async (questionId, optionIndex) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: optionIndex
    }));

    // Auto-save response
    try {
      await submitResponse({
        attemptId,
        questionId,
        selectedOption: optionIndex,
        timeSpent: 30 // placeholder time
      });
    } catch (error) {
      console.error("Failed to save response:", error);
    }
  };

  const handleSubmitExam = async () => {
    if (!confirm("Are you sure you want to submit the exam? This action cannot be undone.")) {
      return;
    }

    setIsSubmitting(true);
    try {
      await submitExam({ attemptId });
      router.push(`/exam/${attemptId}/result`);
    } catch (error) {
      alert("Failed to submit exam: " + error.message);
      setIsSubmitting(false);
    }
  };

  const handleAutoSubmit = async () => {
    try {
      await submitExam({ attemptId });
      router.push(`/exam/${attemptId}/result`);
    } catch (error) {
      console.error("Auto-submit failed:", error);
    }
  };

  if (!attempt || !questions) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading exam...</p>
        </div>
      </div>
    );
  }

  if (attempt.status !== "in_progress") {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center bg-white p-8 rounded-lg shadow-md">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            Exam Not Available
          </h1>
          <p className="text-gray-600 mb-6">
            This exam is not currently in progress or has already been completed.
          </p>
          <a
            href="/dashboard"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go to Dashboard
          </a>
        </div>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === questions.length - 1;
  const isFirstQuestion = currentQuestionIndex === 0;

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-md">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-xl font-bold text-gray-800">
                Online Examination
              </h1>
              <p className="text-sm text-gray-600">
                Question {currentQuestionIndex + 1} of {questions.length}
              </p>
            </div>
            
            <div className="flex items-center space-x-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {formatTime(timeRemaining)}
                </div>
                <div className="text-xs text-gray-600">Time Remaining</div>
              </div>
              
              <button
                onClick={handleSubmitExam}
                disabled={isSubmitting}
                className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors"
              >
                {isSubmitting ? "Submitting..." : "Submit Exam"}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Question Panel */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-md p-8">
              <div className="mb-6">
                <div className="flex justify-between items-center mb-4">
                  <span className="text-sm font-medium text-gray-500">
                    Question {currentQuestionIndex + 1}
                  </span>
                  <span className="text-sm font-medium text-blue-600">
                    {currentQuestion?.marks} Mark{currentQuestion?.marks !== 1 ? 's' : ''}
                  </span>
                </div>
                
                <h2 className="text-xl font-semibold text-gray-800 mb-6">
                  {currentQuestion?.questionText}
                </h2>
              </div>

              <div className="space-y-4 mb-8">
                {currentQuestion?.options.map((option, index) => (
                  <label
                    key={index}
                    className={`block p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      selectedAnswers[currentQuestion._id] === index
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <input
                        type="radio"
                        name={`question-${currentQuestion._id}`}
                        value={index}
                        checked={selectedAnswers[currentQuestion._id] === index}
                        onChange={() => handleAnswerSelect(currentQuestion._id, index)}
                        className="mr-3"
                      />
                      <span className="text-gray-800">{option.text}</span>
                    </div>
                  </label>
                ))}
              </div>

              {/* Navigation */}
              <div className="flex justify-between">
                <button
                  onClick={() => setCurrentQuestionIndex(prev => Math.max(0, prev - 1))}
                  disabled={isFirstQuestion}
                  className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  Previous
                </button>
                
                <button
                  onClick={() => setCurrentQuestionIndex(prev => Math.min(questions.length - 1, prev + 1))}
                  disabled={isLastQuestion}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  Next
                </button>
              </div>
            </div>
          </div>

          {/* Question Navigator */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                Question Navigator
              </h3>
              
              <div className="grid grid-cols-5 gap-2 mb-6">
                {questions.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentQuestionIndex(index)}
                    className={`w-10 h-10 rounded text-sm font-medium transition-colors ${
                      index === currentQuestionIndex
                        ? 'bg-blue-600 text-white'
                        : selectedAnswers[questions[index]._id] !== undefined
                        ? 'bg-green-100 text-green-800 border border-green-300'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {index + 1}
                  </button>
                ))}
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-green-100 border border-green-300 rounded mr-2"></div>
                  <span>Answered</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-gray-100 rounded mr-2"></div>
                  <span>Not Answered</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-blue-600 rounded mr-2"></div>
                  <span>Current</span>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t">
                <div className="text-sm space-y-2">
                  <div className="flex justify-between">
                    <span>Answered:</span>
                    <span className="font-medium text-green-600">
                      {Object.keys(selectedAnswers).length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Not Answered:</span>
                    <span className="font-medium text-red-600">
                      {questions.length - Object.keys(selectedAnswers).length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total:</span>
                    <span className="font-medium">{questions.length}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
