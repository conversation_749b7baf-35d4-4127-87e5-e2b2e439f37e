// Simple script to seed the database with sample examinations
// Run this with: node scripts/seed.js

const { ConvexHttpClient } = require("convex/browser");

const client = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL || "https://valuable-dolphin-642.convex.cloud");

async function seedDatabase() {
  try {
    console.log("🌱 Seeding database with sample examinations...");
    
    const result = await client.mutation("seedData:seedExaminations", {});
    
    console.log("✅ Database seeded successfully!");
    console.log(`📊 Inserted ${result.insertedCount} examinations`);
    console.log("🎯 You can now view the examinations on the homepage");
    
  } catch (error) {
    console.error("❌ Error seeding database:", error);
  }
}

seedDatabase();
