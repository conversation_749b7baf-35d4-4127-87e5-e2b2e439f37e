import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Seed sample examinations
export const seedExaminations = mutation({
  args: {},
  handler: async (ctx) => {
    const now = new Date().toISOString();

    // Sample JKSSB examinations
    const jkssbExams = [
      {
        title: "JKSSB Class IV Recruitment 2024",
        description: "Recruitment for Class IV posts including Peon, Chowkidar, Mali, Sweeper, and other Group D positions in various government departments.",
        examType: "JKSSB",
        category: "Class IV",
        totalSeats: 2500,
        availableSeats: 2500,
        registrationStartDate: "2024-08-01T00:00:00.000Z",
        registrationEndDate: "2024-08-31T23:59:59.000Z",
        examDate: "2024-09-15T00:00:00.000Z",
        examTime: "10:00 AM",
        duration: 90,
        totalQuestions: 100,
        marksPerQuestion: 1,
        negativeMarking: false,
        syllabus: [
          "General Knowledge",
          "Current Affairs",
          "Basic Mathematics",
          "English Language",
          "Urdu Language",
          "Hindi Language"
        ],
        eligibilityCriteria: [
          "Age: 18-40 years",
          "Education: 10th Pass",
          "Domicile: J&K UT"
        ],
        applicationFee: 350,
        status: "registration_open",
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "JKSSB Junior Assistant Recruitment 2024",
        description: "Recruitment for Junior Assistant posts in various government departments of Jammu and Kashmir.",
        examType: "JKSSB",
        category: "Non-Gazetted",
        totalSeats: 1200,
        availableSeats: 1200,
        registrationStartDate: "2024-07-15T00:00:00.000Z",
        registrationEndDate: "2024-08-15T23:59:59.000Z",
        examDate: "2024-09-20T00:00:00.000Z",
        examTime: "02:00 PM",
        duration: 120,
        totalQuestions: 120,
        marksPerQuestion: 1,
        negativeMarking: true,
        negativeMarks: 0.25,
        syllabus: [
          "General Knowledge",
          "Current Affairs",
          "Reasoning",
          "Quantitative Aptitude",
          "English Language",
          "Computer Knowledge"
        ],
        eligibilityCriteria: [
          "Age: 18-35 years",
          "Education: Graduate",
          "Domicile: J&K UT"
        ],
        applicationFee: 500,
        status: "registration_open",
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "JKSSB Police Constable Recruitment 2024",
        description: "Recruitment for Police Constable posts in Jammu and Kashmir Police Department.",
        examType: "JKSSB",
        category: "Police",
        totalSeats: 3500,
        availableSeats: 3500,
        registrationStartDate: "2024-08-10T00:00:00.000Z",
        registrationEndDate: "2024-09-10T23:59:59.000Z",
        examDate: "2024-10-05T00:00:00.000Z",
        examTime: "10:00 AM",
        duration: 180,
        totalQuestions: 100,
        marksPerQuestion: 2,
        negativeMarking: true,
        negativeMarks: 0.5,
        syllabus: [
          "General Knowledge",
          "Current Affairs",
          "Reasoning",
          "Numerical Ability",
          "General Science",
          "History of J&K"
        ],
        eligibilityCriteria: [
          "Age: 18-28 years",
          "Education: 12th Pass",
          "Physical Standards Required",
          "Domicile: J&K UT"
        ],
        applicationFee: 400,
        status: "upcoming",
        createdAt: now,
        updatedAt: now,
      }
    ];

    // Sample JKPSC examinations
    const jkpscExams = [
      {
        title: "JKPSC KAS (Kashmir Administrative Service) 2024",
        description: "Combined Competitive Examination for recruitment to Kashmir Administrative Service and other allied services.",
        examType: "JKPSC",
        category: "Gazetted",
        totalSeats: 150,
        availableSeats: 150,
        registrationStartDate: "2024-07-01T00:00:00.000Z",
        registrationEndDate: "2024-08-01T23:59:59.000Z",
        examDate: "2024-09-25T00:00:00.000Z",
        examTime: "10:00 AM",
        duration: 180,
        totalQuestions: 150,
        marksPerQuestion: 2,
        negativeMarking: true,
        negativeMarks: 0.66,
        syllabus: [
          "General Studies",
          "Current Affairs",
          "Indian Polity",
          "Economics",
          "Geography",
          "History",
          "Science & Technology",
          "Environment & Ecology"
        ],
        eligibilityCriteria: [
          "Age: 21-35 years",
          "Education: Graduate",
          "Domicile: J&K UT"
        ],
        applicationFee: 800,
        status: "registration_open",
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "JKPSC Assistant Professor Recruitment 2024",
        description: "Recruitment for Assistant Professor posts in various subjects in Government Colleges of J&K.",
        examType: "JKPSC",
        category: "Teaching",
        totalSeats: 500,
        availableSeats: 500,
        registrationStartDate: "2024-08-15T00:00:00.000Z",
        registrationEndDate: "2024-09-15T23:59:59.000Z",
        examDate: "2024-10-20T00:00:00.000Z",
        examTime: "02:00 PM",
        duration: 150,
        totalQuestions: 100,
        marksPerQuestion: 3,
        negativeMarking: true,
        negativeMarks: 1,
        syllabus: [
          "Subject Specific Knowledge",
          "Teaching Methodology",
          "Research Aptitude",
          "General Awareness",
          "Higher Education System"
        ],
        eligibilityCriteria: [
          "Age: 21-40 years",
          "Education: Post Graduate with NET/SET",
          "Domicile: J&K UT"
        ],
        applicationFee: 1000,
        status: "upcoming",
        createdAt: now,
        updatedAt: now,
      }
    ];

    // Insert all examinations
    const allExams = [...jkssbExams, ...jkpscExams];
    const insertedExams = [];

    for (const exam of allExams) {
      const examId = await ctx.db.insert("examinations", exam);
      insertedExams.push(examId);
    }

    // Seed questions for the first examination
    if (insertedExams.length > 0) {
      const firstExamId = insertedExams[0];
      await ctx.runMutation("questions:seedQuestionsForExam", {
        examinationId: firstExamId
      });
    }

    return {
      message: "Sample examinations and questions seeded successfully",
      insertedCount: insertedExams.length,
      examIds: insertedExams,
    };
  },
});
