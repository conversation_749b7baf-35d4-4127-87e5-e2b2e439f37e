import { mutation } from "./_generated/server";

export const seedExaminations = mutation({
  args: {},
  handler: async (ctx) => {
    const now = new Date().toISOString();

    // Sample JKSSB examinations
    const jkssbExams = [
      {
        title: "JKSSB Class IV Recruitment 2024",
        description: "Recruitment for Class IV posts including Peon, Chowkidar, Mali, Sweeper, and other Group D positions in various government departments.",
        examType: "JKSSB",
        category: "Class IV",
        totalSeats: 2500,
        availableSeats: 2500,
        registrationStartDate: "2024-08-01T00:00:00.000Z",
        registrationEndDate: "2024-08-31T23:59:59.000Z",
        examDate: "2024-09-15T00:00:00.000Z",
        examTime: "10:00 AM",
        duration: 90,
        totalQuestions: 100,
        marksPerQuestion: 1,
        negativeMarking: false,
        syllabus: [
          "General Knowledge",
          "Current Affairs",
          "Basic Mathematics",
          "English Language",
          "Urdu Language",
          "Hindi Language"
        ],
        eligibilityCriteria: [
          "Age: 18-40 years",
          "Education: 10th Pass",
          "Domicile: J&K UT"
        ],
        applicationFee: 250,
        status: "registration_open",
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "JKSSB Junior Assistant Recruitment 2024",
        description: "Recruitment for Junior Assistant posts in various government departments across Jammu and Kashmir.",
        examType: "JKSSB",
        category: "Non-Gazetted",
        totalSeats: 500,
        availableSeats: 500,
        registrationStartDate: "2024-07-15T00:00:00.000Z",
        registrationEndDate: "2024-08-15T23:59:59.000Z",
        examDate: "2024-09-20T00:00:00.000Z",
        examTime: "2:00 PM",
        duration: 120,
        totalQuestions: 120,
        marksPerQuestion: 1,
        negativeMarking: true,
        negativeMarks: 0.25,
        syllabus: [
          "General Knowledge",
          "Current Affairs",
          "English Language",
          "Computer Knowledge",
          "Quantitative Aptitude",
          "Reasoning"
        ],
        eligibilityCriteria: [
          "Age: 18-35 years",
          "Education: Graduate",
          "Domicile: J&K UT"
        ],
        applicationFee: 500,
        status: "registration_open",
        createdAt: now,
        updatedAt: now,
      }
    ];

    // Sample JKPSC examinations
    const jkpscExams = [
      {
        title: "JKPSC KAS (Kashmir Administrative Service) 2024",
        description: "Combined Competitive Examination for recruitment to Kashmir Administrative Service and other allied services.",
        examType: "JKPSC",
        category: "Gazetted",
        totalSeats: 150,
        availableSeats: 150,
        registrationStartDate: "2024-07-01T00:00:00.000Z",
        registrationEndDate: "2024-08-01T23:59:59.000Z",
        examDate: "2024-09-25T00:00:00.000Z",
        examTime: "10:00 AM",
        duration: 180,
        totalQuestions: 150,
        marksPerQuestion: 2,
        negativeMarking: true,
        negativeMarks: 0.66,
        syllabus: [
          "General Studies",
          "Current Affairs",
          "Indian Polity",
          "Economics",
          "Geography",
          "History",
          "Science & Technology",
          "Environment & Ecology"
        ],
        eligibilityCriteria: [
          "Age: 21-32 years",
          "Education: Graduate",
          "Domicile: J&K UT"
        ],
        applicationFee: 1000,
        status: "registration_open",
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "JKPSC Lecturer Recruitment 2024",
        description: "Recruitment for Lecturer posts in Government Degree Colleges across Jammu and Kashmir.",
        examType: "JKPSC",
        category: "Gazetted",
        totalSeats: 200,
        availableSeats: 200,
        registrationStartDate: "2024-06-01T00:00:00.000Z",
        registrationEndDate: "2024-07-01T23:59:59.000Z",
        examDate: "2024-08-30T00:00:00.000Z",
        examTime: "10:00 AM",
        duration: 150,
        totalQuestions: 100,
        marksPerQuestion: 2,
        negativeMarking: true,
        negativeMarks: 0.5,
        syllabus: [
          "Subject Specialization",
          "General Awareness",
          "Teaching Methodology",
          "Research Aptitude",
          "Communication Skills"
        ],
        eligibilityCriteria: [
          "Age: 21-40 years",
          "Education: Post Graduate with NET/SET",
          "Domicile: J&K UT"
        ],
        applicationFee: 1000,
        status: "upcoming",
        createdAt: now,
        updatedAt: now,
      }
    ];

    // Insert all examinations
    const allExams = [...jkssbExams, ...jkpscExams];
    const insertedExams = [];

    for (const exam of allExams) {
      const examId = await ctx.db.insert("examinations", exam);
      insertedExams.push(examId);
    }

    return {
      message: "Sample examinations seeded successfully",
      insertedCount: insertedExams.length,
      examIds: insertedExams,
    };
  },
});
