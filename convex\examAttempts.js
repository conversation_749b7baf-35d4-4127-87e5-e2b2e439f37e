import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Start an exam attempt
export const startExamAttempt = mutation({
  args: {
    aspirantId: v.id("aspirants"),
    examinationId: v.id("examinations"),
    registrationId: v.id("examRegistrations"),
  },
  handler: async (ctx, args) => {
    // Check if registration exists and is approved
    const registration = await ctx.db.get(args.registrationId);
    if (!registration) {
      throw new Error("Registration not found");
    }
    if (registration.applicationStatus !== "approved") {
      throw new Error("Your application is not approved yet");
    }
    if (!registration.admitCardGenerated) {
      throw new Error("Admit card not generated yet");
    }

    // Check if exam attempt already exists
    const existingAttempt = await ctx.db
      .query("examAttempts")
      .withIndex("by_registration", (q) => q.eq("registrationId", args.registrationId))
      .first();
    
    if (existingAttempt) {
      throw new Error("You have already attempted this examination");
    }

    // Get examination details
    const examination = await ctx.db.get(args.examinationId);
    if (!examination) {
      throw new Error("Examination not found");
    }

    const now = new Date().toISOString();
    
    return await ctx.db.insert("examAttempts", {
      aspirantId: args.aspirantId,
      examinationId: args.examinationId,
      registrationId: args.registrationId,
      startTime: now,
      status: "in_progress",
      totalQuestions: examination.totalQuestions,
      attemptedQuestions: 0,
      correctAnswers: 0,
      wrongAnswers: 0,
      unansweredQuestions: examination.totalQuestions,
      totalMarks: examination.totalQuestions * examination.marksPerQuestion,
      obtainedMarks: 0,
      percentage: 0,
      createdAt: now,
    });
  },
});

// Submit an exam attempt
export const submitExamAttempt = mutation({
  args: { attemptId: v.id("examAttempts") },
  handler: async (ctx, args) => {
    const attempt = await ctx.db.get(args.attemptId);
    if (!attempt) {
      throw new Error("Exam attempt not found");
    }
    
    if (attempt.status !== "in_progress") {
      throw new Error("Exam is not in progress");
    }

    // Get all responses for this attempt
    const responses = await ctx.db
      .query("examResponses")
      .withIndex("by_attempt", (q) => q.eq("attemptId", args.attemptId))
      .collect();

    // Calculate results
    let correctAnswers = 0;
    let wrongAnswers = 0;
    let obtainedMarks = 0;

    for (const response of responses) {
      if (response.isCorrect === true) {
        correctAnswers++;
        obtainedMarks += response.marksAwarded;
      } else if (response.isCorrect === false) {
        wrongAnswers++;
        obtainedMarks += response.marksAwarded; // This could be negative for wrong answers
      }
    }

    const attemptedQuestions = responses.length;
    const unansweredQuestions = attempt.totalQuestions - attemptedQuestions;
    const percentage = (obtainedMarks / attempt.totalMarks) * 100;

    const now = new Date().toISOString();

    return await ctx.db.patch(args.attemptId, {
      endTime: now,
      status: "completed",
      attemptedQuestions,
      correctAnswers,
      wrongAnswers,
      unansweredQuestions,
      obtainedMarks,
      percentage: Math.max(0, percentage), // Ensure percentage is not negative
    });
  },
});

// Auto-submit exam attempt (when time expires)
export const autoSubmitExamAttempt = mutation({
  args: { attemptId: v.id("examAttempts") },
  handler: async (ctx, args) => {
    const attempt = await ctx.db.get(args.attemptId);
    if (!attempt) {
      throw new Error("Exam attempt not found");
    }
    
    if (attempt.status !== "in_progress") {
      return attempt; // Already submitted
    }

    // Calculate results similar to submitExamAttempt
    const responses = await ctx.db
      .query("examResponses")
      .withIndex("by_attempt", (q) => q.eq("attemptId", args.attemptId))
      .collect();

    let correctAnswers = 0;
    let wrongAnswers = 0;
    let obtainedMarks = 0;

    for (const response of responses) {
      if (response.isCorrect === true) {
        correctAnswers++;
        obtainedMarks += response.marksAwarded;
      } else if (response.isCorrect === false) {
        wrongAnswers++;
        obtainedMarks += response.marksAwarded;
      }
    }

    const attemptedQuestions = responses.length;
    const unansweredQuestions = attempt.totalQuestions - attemptedQuestions;
    const percentage = (obtainedMarks / attempt.totalMarks) * 100;

    const now = new Date().toISOString();

    return await ctx.db.patch(args.attemptId, {
      endTime: now,
      status: "auto_submitted",
      attemptedQuestions,
      correctAnswers,
      wrongAnswers,
      unansweredQuestions,
      obtainedMarks,
      percentage: Math.max(0, percentage),
    });
  },
});

// Get exam attempt by ID
export const getExamAttempt = query({
  args: { attemptId: v.id("examAttempts") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.attemptId);
  },
});

// Get exam attempts for an aspirant
export const getAspirantExamAttempts = query({
  args: { aspirantId: v.id("aspirants") },
  handler: async (ctx, args) => {
    const attempts = await ctx.db
      .query("examAttempts")
      .withIndex("by_aspirant", (q) => q.eq("aspirantId", args.aspirantId))
      .collect();

    // Get examination details for each attempt
    const attemptsWithExams = await Promise.all(
      attempts.map(async (attempt) => {
        const examination = await ctx.db.get(attempt.examinationId);
        return {
          ...attempt,
          examination,
        };
      })
    );

    return attemptsWithExams;
  },
});

// Get exam attempts for an examination
export const getExaminationAttempts = query({
  args: { examinationId: v.id("examinations") },
  handler: async (ctx, args) => {
    const attempts = await ctx.db
      .query("examAttempts")
      .withIndex("by_examination", (q) => q.eq("examinationId", args.examinationId))
      .collect();

    // Get aspirant details for each attempt
    const attemptsWithAspirants = await Promise.all(
      attempts.map(async (attempt) => {
        const aspirant = await ctx.db.get(attempt.aspirantId);
        return {
          ...attempt,
          aspirant,
        };
      })
    );

    return attemptsWithAspirants;
  },
});

// Calculate and update ranks for an examination
export const calculateRanks = mutation({
  args: { examinationId: v.id("examinations") },
  handler: async (ctx, args) => {
    const attempts = await ctx.db
      .query("examAttempts")
      .withIndex("by_examination", (q) => q.eq("examinationId", args.examinationId))
      .filter((q) => q.neq(q.field("status"), "in_progress"))
      .collect();

    // Sort by obtained marks (descending) and then by time taken (ascending)
    attempts.sort((a, b) => {
      if (b.obtainedMarks !== a.obtainedMarks) {
        return b.obtainedMarks - a.obtainedMarks;
      }
      // If marks are same, rank by time taken (earlier submission gets better rank)
      return new Date(a.endTime || a.createdAt) - new Date(b.endTime || b.createdAt);
    });

    // Update ranks
    const updatePromises = attempts.map((attempt, index) => {
      return ctx.db.patch(attempt._id, { rank: index + 1 });
    });

    await Promise.all(updatePromises);

    return {
      message: "Ranks calculated successfully",
      totalAttempts: attempts.length,
    };
  },
});
