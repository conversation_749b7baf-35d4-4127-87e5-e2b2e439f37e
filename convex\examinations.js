import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Get all examinations with optional filtering
export const getExaminations = query({
  args: {
    examType: v.optional(v.union(v.literal("JKSSB"), v.literal("JKPSC"))),
    status: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    let examinations = ctx.db.query("examinations");
    
    if (args.examType) {
      examinations = examinations.filter((q) => q.eq(q.field("examType"), args.examType));
    }
    
    if (args.status) {
      examinations = examinations.filter((q) => q.eq(q.field("status"), args.status));
    }
    
    return await examinations.order("desc").collect();
  },
});

// Get a single examination by ID
export const getExaminationById = query({
  args: { id: v.id("examinations") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Get upcoming examinations for homepage cards
export const getUpcomingExaminations = query({
  args: {},
  handler: async (ctx) => {
    const now = new Date().toISOString();
    return await ctx.db
      .query("examinations")
      .filter((q) => 
        q.or(
          q.eq(q.field("status"), "upcoming"),
          q.eq(q.field("status"), "registration_open")
        )
      )
      .order("asc")
      .take(12); // Limit to 12 cards for homepage
  },
});

// Create a new examination (admin function)
export const createExamination = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    examType: v.union(v.literal("JKSSB"), v.literal("JKPSC")),
    category: v.string(),
    totalSeats: v.number(),
    registrationStartDate: v.string(),
    registrationEndDate: v.string(),
    examDate: v.string(),
    examTime: v.string(),
    duration: v.number(),
    totalQuestions: v.number(),
    marksPerQuestion: v.number(),
    negativeMarking: v.boolean(),
    negativeMarks: v.optional(v.number()),
    syllabus: v.array(v.string()),
    eligibilityCriteria: v.array(v.string()),
    applicationFee: v.number(),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    
    // Determine status based on registration dates
    const currentDate = new Date();
    const regStartDate = new Date(args.registrationStartDate);
    const regEndDate = new Date(args.registrationEndDate);
    
    let status = "upcoming";
    if (currentDate >= regStartDate && currentDate <= regEndDate) {
      status = "registration_open";
    } else if (currentDate > regEndDate) {
      status = "registration_closed";
    }
    
    return await ctx.db.insert("examinations", {
      ...args,
      availableSeats: args.totalSeats,
      status,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Update examination status
export const updateExaminationStatus = mutation({
  args: {
    id: v.id("examinations"),
    status: v.union(
      v.literal("upcoming"),
      v.literal("registration_open"),
      v.literal("registration_closed"),
      v.literal("exam_completed"),
      v.literal("results_declared")
    ),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    return await ctx.db.patch(args.id, {
      status: args.status,
      updatedAt: now,
    });
  },
});

// Update available seats when someone registers
export const updateAvailableSeats = mutation({
  args: {
    id: v.id("examinations"),
    seatsToDeduct: v.number(),
  },
  handler: async (ctx, args) => {
    const exam = await ctx.db.get(args.id);
    if (!exam) {
      throw new Error("Examination not found");
    }
    
    const newAvailableSeats = Math.max(0, exam.availableSeats - args.seatsToDeduct);
    const now = new Date().toISOString();
    
    return await ctx.db.patch(args.id, {
      availableSeats: newAvailableSeats,
      updatedAt: now,
    });
  },
});

// Get examinations by category
export const getExaminationsByCategory = query({
  args: { category: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("examinations")
      .filter((q) => q.eq(q.field("category"), args.category))
      .order("desc")
      .collect();
  },
});

// Search examinations
export const searchExaminations = query({
  args: { searchTerm: v.string() },
  handler: async (ctx, args) => {
    const examinations = await ctx.db.query("examinations").collect();
    
    return examinations.filter(exam => 
      exam.title.toLowerCase().includes(args.searchTerm.toLowerCase()) ||
      exam.description.toLowerCase().includes(args.searchTerm.toLowerCase()) ||
      exam.category.toLowerCase().includes(args.searchTerm.toLowerCase())
    );
  },
});
